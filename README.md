# Video Background Filter - Technical Assessment

A full-stack application that applies real-time background filtering to video content while keeping people in full color. This system uses face detection to identify speakers and applies a grayscale filter to the background only.

## ✨ Features

- **Real-time Video Processing**: Processes video frames at 5 FPS for smooth performance
- **Face Detection**: Uses OpenCV's Haar Cascade classifier to detect faces/people
- **Background Filtering**: Applies grayscale filter to background while preserving person in color
- **Live Statistics**: Shows detection results, processing time, and confidence metrics
- **Responsive UI**: Modern React interface with real-time overlays

## 🏗️ Project Structure

### Backend (`/backend`)
- **Technology**: Python Flask with OpenCV
- **Purpose**: Video processing and face detection API
- **Key Files**:
  - `main.py` - Flask application with video processing endpoints
  - `helpers.py` - Face detection and image processing utilities
  - `requirements.txt` - Python dependencies

### Frontend (`/frontend`)
- **Technology**: React with TypeScript
- **Purpose**: Video player with real-time processing interface
- **Key Files**:
  - `src/App.tsx` - Main application with video processing logic
  - `src/components/` - Video player, detection overlay, and statistics components
  - `src/consts.ts` - Configuration constants (video URL)

## 🚀 Getting Started

### Prerequisites
- Python 3.8+
- Node.js 16+
- npm or yarn

### Backend Setup

1. Navigate to the project root directory
2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Start the backend server:
   ```bash
   python backend/main.py
   ```

The backend will run on `http://127.0.0.1:8080`

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install Node.js dependencies:
   ```bash
   npm install
   ```

3. Start the React development server:
   ```bash
   npm start
   ```

The frontend will run on `http://localhost:3000`

### Quick Test

1. Open your browser to `http://localhost:3000`
2. Wait for the video to load
3. Click "Start Background Filter" to begin real-time processing
4. You should see the background turn grayscale while people remain in color

## 📡 API Endpoints

### Backend Routes
- `GET /health` - Health check endpoint
- `GET /hello-world` - Test endpoint to verify backend connectivity
- `POST /process-frame` - Process video frame for face detection and background filtering

#### Process Frame Endpoint
```bash
POST /process-frame
Content-Type: application/json

{
  "frame": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

**Response:**
```json
{
  "success": true,
  "filtered_image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "faces": [
    {
      "id": "face_0",
      "x": 250,
      "y": 150,
      "width": 140,
      "height": 180,
      "confidence": 0.85,
      "label": "Face"
    }
  ],
  "face_count": 1,
  "processing_time": 245
}
```

## 🎯 How It Works

1. **Video Capture**: Frontend captures video frames using HTML5 Canvas
2. **Frame Processing**: Frames are sent to backend as base64-encoded images
3. **Face Detection**: OpenCV Haar Cascade detects faces in each frame
4. **Background Filtering**: Grayscale filter applied to background, faces kept in color
5. **Real-time Display**: Processed frames displayed as overlay on original video

## 🔧 Technical Implementation

### Face Detection
- Uses OpenCV's `haarcascade_frontalface_default.xml` classifier
- Processes frames at 5 FPS for optimal performance
- Detects faces with minimum size of 30x30 pixels

### Background Filtering
- Converts entire frame to grayscale
- Restores original color in detected face regions
- Adds padding around faces for natural effect

### Performance Optimization
- Frame processing limited to 5 FPS (200ms intervals)
- Canvas-based frame capture for efficiency
- Asynchronous processing to maintain UI responsiveness

## 🧪 Testing

Run the backend test suite:
```bash
python test_backend.py
```

This will test:
- Health endpoint connectivity
- Frame processing functionality
- Face detection accuracy
- Response time performance

## 🛠️ Technologies Used

- **Backend**: Python, Flask, OpenCV, NumPy, Pillow
- **Frontend**: React, TypeScript, HTML5 Canvas/Video
- **Styling**: CSS3 with modern responsive design
- **Development**: Hot reload for both frontend and backend

## 📝 Assessment Completion

✅ **Core Requirements Implemented:**
- Person detection using Haar Cascade
- Background segmentation and filtering
- Selective filtering (grayscale background, color person)
- Real-time video processing and display

✅ **Technical Features:**
- RESTful API for video processing
- Real-time frame capture and processing
- Face detection with bounding boxes
- Performance monitoring and statistics
- Responsive user interface

## 🚀 Future Enhancements

Potential improvements for production use:
- Use more advanced face detection (MediaPipe, MTCNN)
- Add multiple filter options (sepia, blur, etc.)
- Implement WebRTC for live camera input
- Add face recognition for person identification
- Optimize processing with GPU acceleration