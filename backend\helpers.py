import logging
import ffmpeg
import os
import uuid
import cv2
import numpy as np
import base64
from io import BytesIO
from PIL import Image

# A lightweight face detection model
face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_temp_path():
    temp_dir = os.path.join(os.path.dirname(__file__), "temp")
    os.makedirs(temp_dir, exist_ok=True)
    random_filename = f"temp_{str(uuid.uuid4())[:8]}"
    return os.path.join(temp_dir, random_filename)

def detect_faces(image):
    """
    Detect faces in an image using Haar Cascade classifier
    Returns list of face rectangles (x, y, w, h)
    """
    try:
        # Convert to grayscale for face detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Detect faces
        faces = face_cascade.detectMultiScale(
            gray,
            scaleFactor=1.1,
            minNeighbors=5,
            minSize=(30, 30),
            flags=cv2.CASCADE_SCALE_IMAGE
        )

        return faces
    except Exception as e:
        logger.error(f"Error in face detection: {e}")
        return []

def apply_background_filter(image, faces):
    """
    Apply grayscale filter to background while keeping faces in color
    """
    try:
        # Create a copy of the original image
        result = image.copy()

        # Convert entire image to grayscale
        gray_image = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        gray_bgr = cv2.cvtColor(gray_image, cv2.COLOR_GRAY2BGR)

        # Start with grayscale background
        result = gray_bgr.copy()

        # For each detected face, restore original color
        for (x, y, w, h) in faces:
            # Add some padding around the face for better effect
            padding = 20
            x_start = max(0, x - padding)
            y_start = max(0, y - padding)
            x_end = min(image.shape[1], x + w + padding)
            y_end = min(image.shape[0], y + h + padding)

            # Restore original color in the face region
            result[y_start:y_end, x_start:x_end] = image[y_start:y_end, x_start:x_end]

        return result
    except Exception as e:
        logger.error(f"Error applying background filter: {e}")
        return image

def process_frame(frame_data):
    """
    Process a single frame: detect faces and apply background filter
    """
    try:
        # Decode base64 image
        image_data = base64.b64decode(frame_data.split(',')[1])
        image = Image.open(BytesIO(image_data))

        # Convert PIL image to OpenCV format
        opencv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # Detect faces
        faces = detect_faces(opencv_image)

        # Apply background filter
        filtered_image = apply_background_filter(opencv_image, faces)

        # Convert back to base64 for frontend
        _, buffer = cv2.imencode('.jpg', filtered_image)
        filtered_base64 = base64.b64encode(buffer).decode('utf-8')

        # Prepare face detection results
        face_detections = []
        for i, (x, y, w, h) in enumerate(faces):
            face_detections.append({
                'id': f'face_{i}',
                'x': int(x),
                'y': int(y),
                'width': int(w),
                'height': int(h),
                'confidence': 0.85,  # Haar cascade doesn't provide confidence, using fixed value
                'label': 'Face'
            })

        return {
            'success': True,
            'filtered_image': f'data:image/jpeg;base64,{filtered_base64}',
            'faces': face_detections,
            'face_count': len(faces)
        }

    except Exception as e:
        logger.error(f"Error processing frame: {e}")
        return {
            'success': False,
            'error': str(e),
            'faces': [],
            'face_count': 0
        }