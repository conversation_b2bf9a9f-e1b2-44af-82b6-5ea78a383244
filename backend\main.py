from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import logging
import time
from helpers import *

app = Flask(__name__)
cors = CORS(app)

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@app.route("/hello-world", methods=["GET"])
def hello_world():
    try:
        return jsonify({"Hello": "World"}), 200
    except Exception as e:
        logger.error(f"Error: {e}")
        return jsonify({"error": str(e)}), 500

@app.route("/process-frame", methods=["POST"])
def process_video_frame():
    """
    Process a single video frame for face detection and background filtering
    """
    try:
        start_time = time.time()

        # Get frame data from request
        data = request.get_json()
        if not data or 'frame' not in data:
            return jsonify({"error": "No frame data provided"}), 400

        frame_data = data['frame']

        # Process the frame
        result = process_frame(frame_data)

        # Calculate processing time
        processing_time = int((time.time() - start_time) * 1000)  # Convert to milliseconds
        result['processing_time'] = processing_time

        if result['success']:
            logger.info(f"Processed frame successfully. Found {result['face_count']} faces. Processing time: {processing_time}ms")
            return jsonify(result), 200
        else:
            logger.error(f"Frame processing failed: {result.get('error', 'Unknown error')}")
            return jsonify(result), 500

    except Exception as e:
        logger.error(f"Error in process_video_frame: {e}")
        return jsonify({
            "success": False,
            "error": str(e),
            "faces": [],
            "face_count": 0,
            "processing_time": 0
        }), 500

@app.route("/health", methods=["GET"])
def health_check():
    """
    Health check endpoint to verify the service is running
    """
    try:
        return jsonify({
            "status": "healthy",
            "service": "video-background-filter",
            "timestamp": time.time()
        }), 200
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=8080, debug=True, use_reloader=False)
