import React, { useRef, useState, useCallback, useEffect } from 'react';
import VideoPlayer from './components/VideoPlayer';
import FaceDetectionOverlay from './components/FaceDetectionOverlay';
import DetectionStats from './components/DetectionStats';
import { videoUrl } from './consts';

export interface FaceDetection {
    id: string;
    x: number;
    y: number;
    width: number;
    height: number;
    confidence: number;
    label?: string;
  }

const App: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const [response, setResponse] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [detections, setDetections] = useState<FaceDetection[]>([]);
  const [processingTime, setProcessingTime] = useState<number>(0);
  const [filteredImageUrl, setFilteredImageUrl] = useState<string>('');

  const captureFrame = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return null;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx || video.paused || video.ended) return null;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current video frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to base64 image data
    return canvas.toDataURL('image/jpeg', 0.8);
  }, []);

  const processFrame = useCallback(async (frameData: string) => {
    try {
      const response = await fetch('http://127.0.0.1:5000/process-frame', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ frame: frameData }),
      });

      const result = await response.json();

      if (result.success) {
        setDetections(result.faces || []);
        setProcessingTime(result.processing_time || 0);
        setFilteredImageUrl(result.filtered_image || '');
      } else {
        console.error('Frame processing failed:', result.error);
        setDetections([]);
      }
    } catch (error) {
      console.error('Error processing frame:', error);
      setDetections([]);
    }
  }, []);

  const startProcessing = useCallback(() => {
    if (isProcessing || !videoRef.current) return;

    setIsProcessing(true);

    // Process frames at 5 FPS (every 200ms) to balance performance and real-time feel
    intervalRef.current = setInterval(() => {
      const frameData = captureFrame();
      if (frameData) {
        processFrame(frameData);
      }
    }, 200);
  }, [isProcessing, captureFrame, processFrame]);

  const stopProcessing = useCallback(() => {
    setIsProcessing(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setDetections([]);
    setFilteredImageUrl('');
  }, []);

  const pingBackend = async () => {
    try {
      const res = await fetch('http://127.0.0.1:5000/hello-world');
      const data = await res.text();
      setResponse(data);
      console.log('Backend response:', data);
    } catch (error) {
      console.error('Error pinging backend:', error);
      setResponse('Error connecting to backend');
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return (
    <div className="container">
      <div style={{ textAlign: 'center' }}>
        <h1>Video Background Filter</h1>
        <p>Real-time face detection with background filtering</p>

        <div className="video-container">
          <VideoPlayer
            ref={videoRef}
            src={videoUrl}
            onLoadedMetadata={() => console.log('Video loaded')}
          />
          <FaceDetectionOverlay detections={detections} videoRef={videoRef} />

          {/* Hidden canvas for frame capture */}
          <canvas ref={canvasRef} style={{ display: 'none' }} />

          {/* Filtered video overlay */}
          {filteredImageUrl && (
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              opacity: 0.8,
              pointerEvents: 'none',
              zIndex: 5
            }}>
              <img
                src={filteredImageUrl}
                alt="Filtered frame"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover'
                }}
              />
            </div>
          )}
        </div>

        <div className="controls">
          <button
            onClick={isProcessing ? stopProcessing : startProcessing}
            className={`btn ${isProcessing ? 'btn-secondary' : 'btn-primary'}`}
            disabled={!videoRef.current}
          >
            {isProcessing ? 'Stop Processing' : 'Start Background Filter'}
          </button>

          <button
            onClick={pingBackend}
            className="btn btn-secondary"
            style={{ marginLeft: '10px' }}
          >
            Test Backend
          </button>
        </div>

        {response && (
          <div style={{ marginTop: '10px', padding: '10px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
            Backend Response: {response}
          </div>
        )}

        <DetectionStats
          detections={detections}
          processingTime={processingTime}
          isDetecting={isProcessing}
        />
      </div>
    </div>
  );
};

export default App;