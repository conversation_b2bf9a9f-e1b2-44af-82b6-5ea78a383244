#!/usr/bin/env python3
"""
Test script to verify the backend video processing endpoint
"""

import requests
import base64
import json
import cv2
import numpy as np
from io import BytesIO
from PIL import Image

def create_test_image_with_face():
    """
    Create a simple test image with a face-like rectangle for testing
    """
    # Create a simple test image (640x480, blue background)
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    img[:, :] = [100, 150, 200]  # Blue background
    
    # Add a face-like rectangle (skin color)
    cv2.rectangle(img, (250, 150), (390, 330), (180, 150, 120), -1)  # Face
    cv2.rectangle(img, (270, 180), (290, 200), (50, 50, 50), -1)     # Left eye
    cv2.rectangle(img, (350, 180), (370, 200), (50, 50, 50), -1)     # Right eye
    cv2.rectangle(img, (310, 220), (330, 240), (100, 80, 80), -1)    # Nose
    cv2.rectangle(img, (290, 270), (350, 290), (150, 100, 100), -1)  # Mouth
    
    return img

def test_backend_endpoint():
    """
    Test the /process-frame endpoint with a sample image
    """
    print("Testing backend video processing endpoint...")
    
    # Create test image
    test_img = create_test_image_with_face()
    
    # Convert to PIL Image and then to base64
    pil_img = Image.fromarray(cv2.cvtColor(test_img, cv2.COLOR_BGR2RGB))
    buffer = BytesIO()
    pil_img.save(buffer, format='JPEG')
    img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    frame_data = f"data:image/jpeg;base64,{img_base64}"
    
    # Test the endpoint
    try:
        response = requests.post(
            'http://127.0.0.1:8080/process-frame',
            json={'frame': frame_data},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Backend test successful!")
            print(f"   - Faces detected: {result.get('face_count', 0)}")
            print(f"   - Processing time: {result.get('processing_time', 0)}ms")
            print(f"   - Success: {result.get('success', False)}")
            
            if result.get('faces'):
                print("   - Face details:")
                for i, face in enumerate(result['faces']):
                    print(f"     Face {i+1}: x={face['x']}, y={face['y']}, w={face['width']}, h={face['height']}")
            
            return True
        else:
            print(f"❌ Backend test failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend test failed with error: {e}")
        return False

def test_health_endpoint():
    """
    Test the /health endpoint
    """
    print("Testing health endpoint...")
    
    try:
        response = requests.get('http://127.0.0.1:8080/health', timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Health check successful!")
            print(f"   - Status: {result.get('status')}")
            print(f"   - Service: {result.get('service')}")
            return True
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed with error: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Backend Testing Suite")
    print("=" * 50)
    
    # Test health endpoint
    health_ok = test_health_endpoint()
    print()
    
    # Test video processing endpoint
    processing_ok = test_backend_endpoint()
    print()
    
    # Summary
    print("=" * 50)
    print("Test Summary:")
    print(f"Health endpoint: {'✅ PASS' if health_ok else '❌ FAIL'}")
    print(f"Processing endpoint: {'✅ PASS' if processing_ok else '❌ FAIL'}")
    
    if health_ok and processing_ok:
        print("\n🎉 All tests passed! Backend is ready for use.")
    else:
        print("\n⚠️  Some tests failed. Please check the backend.")
    
    print("=" * 50)
